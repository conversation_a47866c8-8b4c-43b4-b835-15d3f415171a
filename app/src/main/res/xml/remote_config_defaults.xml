<?xml version="1.0" encoding="UTF-8"?>
<defaults>
    <entry>
        <key>IS_AD_MULTI_IDS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>NUM_COINS_PURCHASE_1500</key>
        <value>1500000</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_SCREEN_VIEW_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IN_APP_UPDATE_MINIMUM_APP_VERSION</key>
        <value>1</value>
    </entry>
    <entry>
        <key>COMM_IN_APP_UPDATE_NEW_VERSION_STALENESS_DAYS_THRESHOLD</key>
        <value>15000</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_OPEN_APP_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_DELAY_INTER_ADS</key>
        <value>10</value>
    </entry>
    <entry>
        <key>SPLASH_TIME_OUT</key>
        <value>2</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_BANNER_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_NATIVE_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>CREDIT_SIGN_IN_REWARD</key>
        <value>2000</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_PAID_AD_IMPRESSION_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>TOKEN_ANSWER_TO_COIN_GPT</key>
        <value>4000</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_USER_PROPERTIES</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IN_APP_UPDATE_ENABLED</key>
        <value>false</value>
    </entry>
    <entry>
        <key>IS_HIDE_SYSTEM_NAV_BAR</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_IAP_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>TOKEN_PROMPT_TO_COIN_GPT</key>
        <value>1000</value>
    </entry>
    <entry>
        <key>COMM_DELAY_INTER_ADS_BYPASS_ALLOWED</key>
        <value>false</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_INTER_OPEN_ADS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_CLICK_BUTTON_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_RATE_ME_SHOW_CLOSE_BUTTON</key>
        <value>false</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_INTER_ACTION_ADS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_INTER_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>NUM_COINS_PURCHASE_53000</key>
        <value>53000000</value>
    </entry>
    <entry>
        <key>MODEL_GEMINI_NAME</key>
        <value>gemini-1.5-pro</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_OPEN_ADS_RESUME</key>
        <value>true</value>
    </entry>
    <entry>
        <key>SHOW_AI_TYPE</key>
        <value>1</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_RATE_ME_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_BANNER_ADS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_RATE_ME_4STAR_PLUS</key>
        <value>3</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_OPEN_ADS_OPEN</key>
        <value>true</value>
    </entry>
    <entry>
        <key>NUM_COINS_PURCHASE_25000</key>
        <value>25000000</value>
    </entry>
    <entry>
        <key>CREDIT_WATCH_REWARD_AD</key>
        <value>5000</value>
    </entry>
    <entry>
        <key>COMM_IN_APP_UPDATE_NEW_VERSION_STALENESS_DAYS_RE_PROMPT</key>
        <value>5000</value>
    </entry>
    <entry>
        <key>COMM_RATE_ME_BELOW_3STAR_PLUS</key>
        <value>5</value>
    </entry>
    <entry>
        <key>TOKEN_ANSWER_TO_COIN_GEMINI</key>
        <value>2000</value>
    </entry>
    <entry>
        <key>NUM_COINS_PURCHASE_5500</key>
        <value>5500000</value>
    </entry>
    <entry>
        <key>IS_USE_GATEWAY_API</key>
        <value>false</value>
    </entry>
    <entry>
        <key>MODEL_GPT_NAME</key>
        <value>gpt-4.1-nano</value>
    </entry>
    <entry>
        <key>COMM_ENABLE_NATIVE_ADS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>NUM_COINS_PURCHASE_12000</key>
        <value>12000000</value>
    </entry>
    <entry>
        <key>IS_SHOW_IAP</key>
        <value>true</value>
    </entry>
    <entry>
        <key>TOKEN_PROMPT_TO_COIN_GEMINI</key>
        <value>500</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_OPEN_APP_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_ANALYTICS</key>
        <value>true</value>
    </entry>
    <entry>
        <key>COMM_IS_ENABLE_ADS_VIDEO_ANALYTICS</key>
        <value>true</value>
    </entry>
</defaults>